import json
import os

from app.enums.task import TaskType
from scripts import COOKIE
from worker.xdoc_html_fix_worker import XdocHtmlFixWorker
import requests
from app.basic.storagehandler import storage
from worker.title_level_worker import TitleLevelWorker
from app.basic.log import logger


class TestXdocHtmlFix:

    def __init__(self, task_id_list: list, is_ai_edit: bool, tag: str):
        self.task_id_list = task_id_list
        self.is_ai_edit = is_ai_edit
        self.tag = tag

    async def main(self):
        total_stat = []
        for tid in self.task_id_list:
            res = requests.get(
                url=f'http://xdoc.open.hexinedu.com/api/admin/taskV2/getOneById',
                params={
                    'taskId': tid
                },
                cookies={'UBUS': 'M4S-CRGoa1rxT7-5YQav_oFK4py54c1xzvLOmHfds1NZSTS2DI7dncKUI7I3ZPuL'}
            ).json()
            task_info = res['data']
            html_url = task_info['html']
            subject = task_info['meta']['subject']
            if self.is_ai_edit:
                machine_html_url = html_url.replace('.html', '.backup.html')
            else:
                machine_html_url = html_url.replace('.html', '.machine.html')

            # 测试 HTML 片段
            # machine_html_url = 'https://hexin-worksheet.oss-cn-shanghai.aliyuncs.com/static/ai-util/test_html_fix.html'

            # 前置跑标题
            # callback_info = TitleLevelWorker(
            #     task_id=tid,
            #     html_url=machine_html_url,
            #     subject=subject,
            #     bucket_name='xdoc-stable',
            #     upload_path='open/fc7539b21810cd4f0f0fb620/task/19123911.html',
            #     is_ai_edit=True,
            #     is_test=True,
            # ).main()
            # machine_html_url = 'https://xdoc-stable.oss-cn-shanghai.aliyuncs.com/open/fc7539b21810cd4f0f0fb620/task/19123911.html'

            app_key = task_info['appKey']
            logger.info(f"task_id={tid}")
            logger.info(f"subject={subject}")
            logger.info(f"app_key={app_key}")
            logger.info(f"html_url={html_url}")
            logger.info(f"machine_html_url={machine_html_url}")
            print()
            callback_info = await XdocHtmlFixWorker(
                task_id=tid,
                html_url=machine_html_url,
                subject=subject,
                app_key=app_key,
                bucket_name='xdoc-stable',
                upload_path='open/fc7539b21810cd4f0f0fb620/task/19809373.html',
                word_type='',
                is_ai_edit=self.is_ai_edit,
                task_type=TaskType.XDOC_HTML_FIX_PRIORITY.value,
                is_test=True,
                tag=self.tag,
            ).main()
            status = callback_info['status']  # 0 成功  1 失败

            if status == 0:
                # 成功
                stat = callback_info['stat']
                total_stat.append(stat)
                print('success.')
            else:
                # 失败
                print(f'{tid} 处理失败，error={callback_info["info"]}')


if __name__ == '__main__':
    import asyncio
    # tag: jxw  daily_fbd

    lst = [
        '20366335'
    ]
    loop = asyncio.get_event_loop()
    loop.run_until_complete(
        TestXdocHtmlFix(
            task_id_list=lst,
            is_ai_edit=False,
            tag='jxw'
        ).main()
    )
