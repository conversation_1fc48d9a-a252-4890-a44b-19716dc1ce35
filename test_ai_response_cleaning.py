#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AI响应清理功能
"""

import sys
import os
import json
import re
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def test_ai_response_cleaning():
    """
    测试AI响应清理功能
    """
    print("=== 测试AI响应清理功能 ===\n")
    
    # 模拟清理函数
    def clean_ai_response(response: str) -> str:
        """
        清理AI响应中的markdown标记和其他格式化字符
        """
        # 去除首尾空白
        cleaned = response.strip()
        
        # 移除可能的markdown代码块标记
        if cleaned.startswith('```json'):
            cleaned = cleaned[7:]  # 移除 '```json'
        elif cleaned.startswith('```'):
            cleaned = cleaned[3:]   # 移除 '```'
        
        if cleaned.endswith('```'):
            cleaned = cleaned[:-3]  # 移除结尾的 '```'
        
        # 再次去除空白
        cleaned = cleaned.strip()
        
        # 移除可能的其他格式化标记
        lines = cleaned.split('\n')
        json_lines = []
        in_json = False
        
        for line in lines:
            line = line.strip()
            # 跳过空行和注释行
            if not line or line.startswith('#') or line.startswith('//'):
                continue
            
            # 检查是否是JSON开始
            if line.startswith('[') or line.startswith('{'):
                in_json = True
            
            if in_json:
                json_lines.append(line)
            
            # 检查是否是JSON结束
            if line.endswith(']') or line.endswith('}'):
                break
        
        # 如果找到了JSON内容，返回拼接的结果
        if json_lines:
            result = '\n'.join(json_lines)
            return result
        
        # 如果没有找到明确的JSON结构，返回原始清理结果
        return cleaned
    
    # 测试用例
    test_cases = [
        {
            'name': '标准JSON响应',
            'input': '["1", "2", "3"]',
            'expected': '["1", "2", "3"]'
        },
        {
            'name': '带```json标记的响应',
            'input': '```json\n["1", "2", "3"]\n```',
            'expected': '["1", "2", "3"]'
        },
        {
            'name': '带```标记的响应',
            'input': '```\n["1", "2", "3"]\n```',
            'expected': '["1", "2", "3"]'
        },
        {
            'name': '带前后文本的响应',
            'input': '根据分析，识别出的ID如下：\n```json\n["1", "2", "3"]\n```\n以上是分析结果。',
            'expected': '["1", "2", "3"]'
        },
        {
            'name': '多行JSON响应',
            'input': '```json\n[\n  "1",\n  "2",\n  "3"\n]\n```',
            'expected': '[\n"1",\n"2",\n"3"\n]'
        },
        {
            'name': '带注释的响应',
            'input': '```json\n// 这是注释\n["1", "2", "3"]\n# 另一个注释\n```',
            'expected': '["1", "2", "3"]'
        },
        {
            'name': '空数组响应',
            'input': '```json\n[]\n```',
            'expected': '[]'
        },
        {
            'name': '带空行的响应',
            'input': '```json\n\n["1", "2", "3"]\n\n```',
            'expected': '["1", "2", "3"]'
        },
        {
            'name': '对象格式响应',
            'input': '```json\n{"ids": ["1", "2", "3"]}\n```',
            'expected': '{"ids": ["1", "2", "3"]}'
        },
        {
            'name': '无标记的纯JSON',
            'input': '  ["1", "2", "3"]  ',
            'expected': '["1", "2", "3"]'
        }
    ]
    
    # 执行测试
    all_passed = True
    for i, test_case in enumerate(test_cases, 1):
        print(f"测试 {i}: {test_case['name']}")
        
        try:
            result = clean_ai_response(test_case['input'])
            
            # 验证清理结果
            if result == test_case['expected']:
                print(f"   ✅ 清理结果正确")
            else:
                print(f"   ❌ 清理结果不正确")
                print(f"   输入: {repr(test_case['input'])}")
                print(f"   期望: {repr(test_case['expected'])}")
                print(f"   实际: {repr(result)}")
                all_passed = False
            
            # 验证JSON解析
            try:
                parsed = json.loads(result)
                print(f"   ✅ JSON解析成功: {parsed}")
            except json.JSONDecodeError as e:
                print(f"   ❌ JSON解析失败: {e}")
                all_passed = False
                
        except Exception as e:
            print(f"   ❌ 清理过程出错: {e}")
            all_passed = False
        
        print()
    
    if all_passed:
        print("🎉 所有AI响应清理测试通过!")
    else:
        print("❌ 部分测试失败")


def test_edge_cases():
    """
    测试边界情况
    """
    print("\n" + "="*50 + "\n")
    print("=== 测试边界情况 ===\n")
    
    def clean_ai_response(response: str) -> str:
        cleaned = response.strip()
        
        if cleaned.startswith('```json'):
            cleaned = cleaned[7:]
        elif cleaned.startswith('```'):
            cleaned = cleaned[3:]
        
        if cleaned.endswith('```'):
            cleaned = cleaned[:-3]
        
        cleaned = cleaned.strip()
        
        lines = cleaned.split('\n')
        json_lines = []
        in_json = False
        
        for line in lines:
            line = line.strip()
            if not line or line.startswith('#') or line.startswith('//'):
                continue
            
            if line.startswith('[') or line.startswith('{'):
                in_json = True
            
            if in_json:
                json_lines.append(line)
            
            if line.endswith(']') or line.endswith('}'):
                break
        
        if json_lines:
            return '\n'.join(json_lines)
        
        return cleaned
    
    edge_cases = [
        {
            'name': '空字符串',
            'input': '',
            'should_parse': False
        },
        {
            'name': '只有标记无内容',
            'input': '```json\n```',
            'should_parse': False
        },
        {
            'name': '无效JSON',
            'input': '```json\n[1, 2, 3\n```',
            'should_parse': False
        },
        {
            'name': '嵌套标记',
            'input': '```json\n```json\n["1", "2"]\n```\n```',
            'should_parse': True
        },
        {
            'name': '多个JSON块',
            'input': '```json\n["1", "2"]\n["3", "4"]\n```',
            'should_parse': True
        }
    ]
    
    for i, case in enumerate(edge_cases, 1):
        print(f"边界测试 {i}: {case['name']}")
        
        try:
            result = clean_ai_response(case['input'])
            print(f"   清理结果: {repr(result)}")
            
            try:
                parsed = json.loads(result)
                if case['should_parse']:
                    print(f"   ✅ 预期可解析，解析成功: {parsed}")
                else:
                    print(f"   ⚠️  预期不可解析，但解析成功: {parsed}")
            except json.JSONDecodeError:
                if not case['should_parse']:
                    print(f"   ✅ 预期不可解析，解析失败（符合预期）")
                else:
                    print(f"   ❌ 预期可解析，但解析失败")
                    
        except Exception as e:
            print(f"   ❌ 处理出错: {e}")
        
        print()


def test_real_world_examples():
    """
    测试真实世界的AI响应示例
    """
    print("\n" + "="*50 + "\n")
    print("=== 测试真实世界示例 ===\n")
    
    def clean_ai_response(response: str) -> str:
        cleaned = response.strip()
        
        if cleaned.startswith('```json'):
            cleaned = cleaned[7:]
        elif cleaned.startswith('```'):
            cleaned = cleaned[3:]
        
        if cleaned.endswith('```'):
            cleaned = cleaned[:-3]
        
        cleaned = cleaned.strip()
        
        lines = cleaned.split('\n')
        json_lines = []
        in_json = False
        
        for line in lines:
            line = line.strip()
            if not line or line.startswith('#') or line.startswith('//'):
                continue
            
            if line.startswith('[') or line.startswith('{'):
                in_json = True
            
            if in_json:
                json_lines.append(line)
            
            if line.endswith(']') or line.endswith('}'):
                break
        
        if json_lines:
            return '\n'.join(json_lines)
        
        return cleaned
    
    # 模拟真实的AI响应
    real_examples = [
        {
            'name': 'GPT风格响应',
            'input': '''根据分析，以下是应该标记为explanation的项目ID：

```json
["2", "3", "7", "8"]
```

这些内容都具有解析性质，应该被重新标记。''',
            'expected_ids': ["2", "3", "7", "8"]
        },
        {
            'name': '豆包风格响应',
            'input': '''```json
["5", "6", "9"]
```''',
            'expected_ids': ["5", "6", "9"]
        },
        {
            'name': '带解释的响应',
            'input': '''经过分析，识别出以下ID需要标记为explanation：

```json
[
    "10",
    "11", 
    "12"
]
```

分析完成。''',
            'expected_ids': ["10", "11", "12"]
        }
    ]
    
    for i, example in enumerate(real_examples, 1):
        print(f"真实示例 {i}: {example['name']}")
        
        try:
            cleaned = clean_ai_response(example['input'])
            parsed = json.loads(cleaned)
            
            if parsed == example['expected_ids']:
                print(f"   ✅ 解析正确: {parsed}")
            else:
                print(f"   ❌ 解析结果不符合预期")
                print(f"   期望: {example['expected_ids']}")
                print(f"   实际: {parsed}")
                
        except Exception as e:
            print(f"   ❌ 处理失败: {e}")
        
        print()


if __name__ == '__main__':
    print("开始测试AI响应清理功能...\n")
    
    # 运行所有测试
    test_ai_response_cleaning()
    test_edge_cases()
    test_real_world_examples()
    
    print("\n测试完成!")
