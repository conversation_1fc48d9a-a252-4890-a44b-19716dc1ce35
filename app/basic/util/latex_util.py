import re
from app.enums.subject import Subject
from app.enums.tag import LatexTag


def extract_latex(data):
    # 提取 latex
    latex_list = []

    def repl(m):
        text = m.group(1)
        latex_list.append(text)
        return '$$###$$'

    data = re.sub(r'\$\$(.*?)\$\$', repl, data)
    return data, latex_list


def restore_latex_v1(data, latex_list):
    # 还原 latex，$$ 内可能混入其他字符串（不是纯###了），但是 latex 的总数不变
    i = 0

    def repl(m):
        nonlocal i
        text = m.group()
        text = text.replace('###', latex_list[i])
        i += 1
        return text

    data = re.sub(r'\$\$(.*?)\$\$', repl, data)
    return data


def restore_latex_v2(data, latex_list):
    # 还原 latex，latex 内容不变，但是 latex 总数可能变了，仅还原之前提取的
    i = 0

    def repl(m):
        nonlocal i
        text = m.group()
        match_latex = latex_list[i]
        if '$$' in match_latex:
            text = text.replace('$$', '')
        text = text.replace('###', match_latex)

        i += 1
        return text

    data = re.sub(r'\$\$###\$\$', repl, data)
    return data


def convert_to_latex(text, subject):
    if subject == Subject.chemistry.name:
        return '$$' + LatexTag.CHANGED.value + '\\mathrm{' + text + '}$$'
    else:
        return f"$${LatexTag.CHANGED.value}{text}$$"


def convert_to_text(s, subject, re_text):
    if subject == Subject.chemistry.name:
        pattern = re.compile(r'\$\$(%s)\\mathrm{(%s)}\$\$' % (f'{LatexTag.CHANGED.value}?', re_text))
        if pattern.search(s):
            s = pattern.sub(lambda x: x.group(1) + x.group(2), s)
        return s
    else:
        pattern = re.compile(r'\$\$(%s%s)\$\$' % (f'{LatexTag.CHANGED.value}?', re_text))
        if pattern.search(s):
            s = pattern.sub(lambda x: x.group(1), s)
        return s


if __name__ == '__main__':
    s = '解析 <span style="font-family:楷体">反应2的平衡常数</span>$$###$$<span style="font-family:楷体">正确；刚性密闭容器，温度不变平衡常数不变，再次达平衡后，容器内各气体分压不变，总压强不变，</span>$$🀐B$$<span style="font-family:楷体">错误；</span>$$###$$<span style="font-family:楷体">，所以总压强为</span>$$###$$<span style="font-family:楷体">正确；达平衡后，缩小体积，平衡逆向移动，温度不变，平衡常数不变，再次平衡后总压强不变，</span>$$🀐\mathrm{D}$$<span style="font-family:楷体">正确。</span>'
    convert_to_text(s, 'math', '[A-F]')
