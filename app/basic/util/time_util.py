from datetime import datetime
import time



def is_time_to_run_dp_official():
    """
    判断当前是否是运行 deepseek 官网 api 的时候
    """
    now = datetime.now()
    # 判断是否在凌晨00:30到8:30之间
    if (now.hour == 0 and now.minute >= 30) or (1 <= now.hour < 8) or (now.hour == 8 and now.minute < 30):
        return True
    return False


def is_output_log():
    if str(time.time()).endswith('6'):
        return True
    return False


if __name__ == '__main__':
    res = is_time_to_run_dp_official()
    print(res)
