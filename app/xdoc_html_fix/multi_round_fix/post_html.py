# -*-coding:utf-8 -*-
# Author     ：wang<PERSON>ua
# Email      ：<EMAIL>
# Time       ：2025/6/16 17:42

import sys
from pathlib import Path

# 获取项目根目录（假设 worker 和 scripts 是同级目录）
project_root = str(Path(__file__).resolve().parent.parent.parent.parent)
sys.path.append(project_root)

from app.basic.util import html_util

import re
import os
from bs4 import BeautifulSoup


class PostHtml:
    """
    后处理 html fix 输出的 html
    """

    def __init__(self, html_data: str):
        self.html_data = html_data
        self.html_list = html_util.split_html_v2(self.html_data)
    

    def is_question(self, tag):
        if not re.search(r'data-label="quest_num"', str(tag)): return False
        return True

    
    def is_answer(self, tag):
        if not re.search(r'data-label="answer"', str(tag)): return False
        return True

    
    def is_analysis(self, tag):
        if not re.search(r'data-label="explanation"', str(tag)): return False
        return True


    def is_question1(self, tag):
        if not re.search(r'data-label="quest_num"', str(tag)): return False
        if not re.search(r'data-level="1"', str(tag)): return False
        return True


    def is_material(self, tag):
        if re.search(r'<hr', str(tag)): return True
        return False


    def is_header(self, tag):
        if re.search(r'data-label="header"', str(tag)): return True
        return False

       
    def clear_n(self):
        html_content = self.html_data
        # - </p>\n
        # - <hr/>\n
        html_content = re.sub(r'(<\/[^<>]+>)\n', lambda m: m.group(1), html_content)
        html_content = re.sub(r'(<\/[^<>]+>)\\n', lambda m: m.group(1), html_content)
        html_content = re.sub(r'(<[^<>]+\/>)\n', lambda m: m.group(1), html_content)
        html_content = re.sub(r'(<[^<>]+\/>)\\n', lambda m: m.group(1), html_content)
        self.html_data = html_content

    
    '''答案分割'''
    def split_answer(self):
         
        def _get_level1_blocks(soup):
            """
            获取完整的一级试题段落，包含后续内容直到遇到:
            1. 新的一级试题
            2. 材料题(data-label="material")
            3. 标题(data-label="header")
            """
            elements = list(soup.find_all(True, recursive=False))
            questions = [q for q in elements if self.is_question1(q)]
            
            blocks = []
            for question in questions:
                block = []
                current = question
                
                # 收集后续节点直到终止条件
                while True:
                    block.append(current)
                    next_sib = current.next_sibling
                    
                    # 终止条件检测
                    if not next_sib or \
                        self.is_question1(next_sib) or \
                        self.is_material(next_sib) or \
                        self.is_header(next_sib):
                        break
                    
                    current = next_sib
                
                blocks.append(block)
            
            return blocks
        
        def _get_blanks(block):
            blanks = []
            for p in block:
                if re.search(r'data-label="answer"', str(p)):
                    break
                blanks.extend(list(p.find_all('span', attrs={'data-label': 'blank'})))
            return blanks
        
        def _get_brackets(block):
            blanks = []
            for p in block:
                if re.search(r'data-label="answer"', str(p)):
                    break
                blanks.extend(list(p.find_all('span', attrs={'data-label': 'bracket'})))
            return blanks
        
        # @description 大小题模式1
        def _get_blanks1(block):
            # - 按照题号分块，若题块有作答空间则计算作答空间
            # - 若没有作答空间则+1，认为是做答题
            # - 遇到答案/解析停止
            groups = []
            group = []
            for p in block:
                if p.has_attr('data-label'):
                    break
                if re.search(r'data-label="quest_num"', str(p)):
                    if group: groups.append(group)
                    group = []
                group.append(p)

            if group: groups.append(group)

            r = []
            for group in groups:
                blanks = []
                for p in group:
                    blanks.extend(list(p.find_all('span', attrs={'data-label': 'blank'})))
                    blanks.extend(list(p.find_all('span', attrs={'data-label': 'bracket'})))
                if \
                    not blanks and \
                    re.search(r'data-label="quest_num"', str(group[0])) and \
                    group[0] != block[0]:
                    blanks.append(group[0])
                r.extend(blanks)

            return r
        
        def _get_answers(block):
            answers = []
            for p in block:
                if len(answers) and not re.search(r'data-label="answer"', str(p)):
                    break
                # answers.extend(list(p.find_all('*', attrs={'data-label': 'answer'})))
                if p.has_attr('data-label') and p['data-label'] == 'answer': answers.append(p)
            return answers
        
        def _split_plain_text(data, blanks, splitter = '\\s+'):
            splitted_answers = []
            for item in data:
                r = f'{splitter}|■|#qnum\\d+#'
                r = re.split(r, item['answer2'])
                r = [a for a in r if a.strip()]
                splitted_answers.extend(r)

            if len(splitted_answers) != len(blanks):
                return []
            
            for item in data:
                new_p = '<p>'
                if re.search(r'^<p[^<>]+>', str(item['answer'])): new_p = re.search(r'^<p[^<>]+>', str(item['answer'])).group(0)
                new_prefix = ''.join(item['prefix_map'])
                r = f'{splitter}|■'
                new_answer = re.split(r, item['answer2'])
                new_answer = [a for a in new_answer if a.strip()]
                new_answer = '■'.join(new_answer)
                new_html = f'{new_p}{new_prefix}{new_answer}</p>'
                new_html = re.sub(r'#latex(\d+)#', lambda m: item['latex_map'][int(m.group(1)) - 1], new_html)
                new_html = re.sub(r'#img(\d+)#', lambda m: item['img_map'][int(m.group(1)) - 1], new_html)
                new_html = re.sub(r'#qnum(\d+)#', lambda m: item['qnum_map'][int(m.group(1)) - 1], new_html)
                item['new_html'] = new_html
            
            return data
        
        # @description 用空格分割简单公式
        def _split_by_space2(data, blanks):
            if not re.search(r'\$\$[^$]+\$\$', ''.join([str(item['answer']) for item in data])):
                return []

            def _rep1(m):
                if not re.search(r'(\\;)+', m.group(0)):
                    return m.group(0)
                r = re.split(r'(\\;)+', m.group(1))
                r = [re.sub(r'\$\$', '', a) for a in r]
                r = [re.sub(r'(\\;)+', '', a) for a in r]
                r = [a for a in r if a]
                if [a for a in r if re.search(r'{|}', re.sub(r'{[^{}]+}', '', a))]:
                    return m.group(0)
                return '■'.join([f'$${a}$$' for a in r])
            
            def _rep2(m):
                if not re.search(r'(\\;)+', m.group(0)):
                    return m.group(0)
                if not re.search(r'^\\[a-z]+{[^{}]+}$', m.group(1)):
                    return m.group(0)
                wrapper = re.search(r'^(\\[a-z]+){([^{}]+)}$', m.group(1)).group(1) # 标签，e.g. \mathrm
                text = re.search(r'^(\\[a-z]+){([^{}]+)}$', m.group(1)).group(2) # 标签里的内容
                r = re.split(r'(\\;)+', text)
                r = [re.sub(r'(\\;)+', '', a) for a in r]
                r = [a for a in r if a]
                if [a for a in r if re.search(r'{|}', re.sub(r'{[^{}]+}', '', a))]:
                    return m.group(0)
                return '■'.join([f'$${wrapper}{{{a}}}$$' for a in r])
            
            def _rep3(m):
                if not re.search(r'(\\;)+', m.group(0)):
                    return m.group(0)
                r = m.group(1)
                r = re.sub(r'(\\[a-z]+){(\\;)+([^{}]+)}', lambda x: f'\\;{x.group(1)}{{{x.group(3)}}}', r)
                r = re.sub(r'(\\[a-z]+){([^{}]+?)(\\;)+}', lambda x: f'{x.group(1)}{{{x.group(2)}}}\\;', r)
                r = re.split(r'(\\;)+', r)
                r = [re.sub(r'(\\;)+', '', a) for a in r]
                r = [a for a in r if a]
                if [a for a in r if re.search(r'{|}', re.sub(r'{[^{}]+}', '', a))]:
                    return m.group(0)
                return '■'.join([f'$${a}$$' for a in r])

            # - 公式里没有标签的情况，e.g. $$\\;2×10^{3}\\;10\\;1.2×10^{3}$$
            # - 公式整个被一个标签包裹的情况
            # - 公式包含多个一级标签的情况，e.g. $$\\mathrm{\\;\\;\\;\\;\\;MMNN}、\\mathrm{MmNN}、\\mathrm{MmNn}、\\mathrm{MMNn\\;\\;\\;\\;\\;}1/7$$
            for item in data:
                answer3 = item['answer1']
                answer3 = re.sub(r'\$\$(\\;)+', ' $$', answer3)
                answer3 = re.sub(r'(\\;)+\$\$', ' $$', answer3)
                answer3 = re.sub(r'\$\$([^$]+)\$\$', _rep1, answer3)
                answer3 = re.sub(r'\$\$([^$]+)\$\$', _rep2, answer3)
                answer3 = re.sub(r'\$\$([^$]+)\$\$', _rep3, answer3)
                item['answer3'] = answer3

            splitted_answers = []
            for item in data:
                # - 保护一下公式内部的\s+
                latex_map = []
                
                def _ref(m):
                    latex_map.append(m.group(0))
                    return f'#latex{len(latex_map)}#'
                
                answers3 = re.sub(r'\$\$[^$]+\$\$', _ref, item['answer3'])
                r = re.split(r'\s+|■|#qnum\d+#|(\\;)+', answers3)
                r = [a for a in r if a]
                r = [re.sub(r'\$\$', '', a) for a in r]
                r = [re.sub(r'(\\;)+', '', a) for a in r]
                r = [a for a in r if a]
                r = [re.sub(r'#latex(\d+)#', lambda x: latex_map[int(x.group(1)) - 1], a) for a in r]
                answers3 = re.sub(r'#latex(\d+)#', lambda x: latex_map[int(x.group(1)) - 1], answers3)
                item['answer3'] = answers3
                splitted_answers.extend(r)
            
            if len(splitted_answers) != len(blanks):
                return []
            
            def _is_balanced(text):
                stack = []
                for char in text:
                    if char == '{':
                        stack.append(char)
                    elif char == '}':
                        if not stack:
                            return False
                        stack.pop()
                return len(stack) == 0

            # - 若每个答案里花括号都是闭合的，则认为是简单场景
            if [a for a in splitted_answers if not _is_balanced(a)]:
                return []

            for item in data:
                new_p = '<p>'
                if re.search(r'^<p[^<>]+>', str(item['answer'])): new_p = re.search(r'^<p[^<>]+>', str(item['answer'])).group(0)
                new_prefix = ''.join(item['prefix_map'])
                # - 保护一下公式内部的\s+
                latex_map = []
                
                def _ref(m):
                    latex_map.append(m.group(0))
                    return f'#latex{len(latex_map)}#'
                
                new_answer = re.sub(r'\$\$[^$]+\$\$', _ref, item['answer3'])

                # - 需要注意这里不能清掉qnum分隔符
                new_answer = re.split(r'\s+|■', new_answer)
                new_answer = [a for a in new_answer if a]
                new_answer = [re.sub(r'#latex(\d+)#', lambda x: latex_map[int(x.group(1)) - 1], a) for a in new_answer]
                new_answer = '■'.join(new_answer)
                new_html = f'{new_p}{new_prefix}{new_answer}</p>'
                new_html = re.sub(r'#img(\d+)#', lambda m: item['img_map'][int(m.group(1)) - 1], new_html)
                new_html = re.sub(r'#qnum(\d+)#', lambda m: item['qnum_map'][int(m.group(1)) - 1], new_html)
                item['new_html'] = new_html
            
            return data
        
        def _execute_split_answer_with_blanks1(block, blanks):
            answers = _get_answers(block)

            current_answer_count = 0
            for answer in answers:
                if re.search(r'■', str(answer)):
                    current_answer_count += len(list(re.findall('■', str(answer)))) + 1
                    continue
                current_answer_count += 1

            # - 不需要分割
            # - 本身分割就是对的
            # - 答案里包含表格暂时不支持
            if len(blanks) <= 1:
                return
            if len(blanks) <= current_answer_count:
                return
            if re.search(r'<table', ''.join([str(a) for a in answers])):
                return

            # - 表格
            # - 图片
            # - 公式
            
            # if re.search(r'31\.', str(block[0])):
            #     print(block)

            # # @todo：目前只支持单P答案的场景。
            # if len(answers) != 1:
            #     return

            data = []
            for answer in answers:
                item = {}
                
                # 保护img标签
                img_map = []

                def _repl1(m):
                    img_map.append(m.group(0))
                    return f'#img{len(img_map)}#'

                answer1 = re.sub(r'<img[^<>]+>', _repl1, str(answer))

                # 保护qnum标签
                qnum_map = []

                def _repl2(m):
                    qnum_map.append(m.group(0))
                    return f'#qnum{len(qnum_map)}#'

                answer1 = re.sub(r'<[^<>]+data-label="quest_num"[^<>]*>.*?<[^<>]+>', _repl2, answer1)
            
                # 保护前缀
                prefix_map = []

                def _repl3(m):
                    prefix_map.append(m.group(0))
                    return ''

                answer1 = re.sub(r'<[^<>]+data-label="discard"[^<>]*>.*?<\/[^<>]+>', _repl3, answer1)

                if re.search(r'^<[^<>]+>(【.*?】)', answer1) and not prefix_map:
                    prefix_map.append(re.search(r'【.*?】', answer1).group(0))
                    answer1 = re.sub(r'^(<[^<>]+>)(【.*?】)', lambda x: x.group(1), answer1)

                # 清理标签
                answer1 = re.sub(r'<[^<>]+>', ' ', answer1)

                # 保护公式
                latex_map = []

                def _repl4(m):
                    latex_map.append(m.group(0))
                    return f'#latex{len(latex_map)}#'

                answer2 = re.sub(r'\$\$[^$]+\$\$', _repl4, answer1)
                item['img_map'] = img_map
                item['qnum_map'] = qnum_map
                item['prefix_map'] = prefix_map
                item['latex_map'] = latex_map
                item['answer1'] = answer1
                item['answer2'] = answer2
                item['answer'] = answer # suop节点
                data.append(item)

            # 内容的一致性检查1
            img_count_1 = 0
            latex_count_1 = 0
            chinese_count_1 = 0
            english_count_1 = 0
            number_count_1 = 0

            # 图片
            # 公式数量
            # 中文
            # 英文
            # 数字
            for item in data:
                img_count_1 += len(re.findall(r'<img', str(item['answer'])))
                latex_count_1 += len(re.findall(r'\$\$[^$]+\$\$', str(item['answer'])))
                content = re.sub(r'<[^<>]+>', '', str(item['answer']))
                content = re.sub(r'\$\$[^$]+\$\$', '', content)
                chinese_count_1 += len(re.findall(r'[\u4e00-\u9fa5]', content))
                english_count_1 += len(re.findall(r'[a-zA-Z]', content))
                number_count_1 += len(re.findall(r'\d', content))
            
            # 按照不同策略分割答案！
           
            # - 普通文本里的空格
            # - 普通文本里的分号
            # - 简单公式里的空格
            if _split_plain_text(data, blanks, '\\s+'): pass
            elif _split_plain_text(data, blanks, '[;；]'): pass
            elif _split_by_space2(data, blanks): pass
            else: return
            
            # 内容的一致性检查2
            img_count_2 = 0
            latex_count_2 = 0
            chinese_count_2 = 0
            english_count_2 = 0
            number_count_2 = 0

            # 图片
            # 公式数量
            # 中文
            # 英文
            # 数字
            for item in data:
                img_count_2 += len(re.findall(r'<img', str(item['new_html'])))
                latex_count_2 += len(re.findall(r'\$\$[^$]+\$\$', str(item['new_html'])))
                content = re.sub(r'<[^<>]+>', '', str(item['new_html']))
                content = re.sub(r'\$\$[^$]+\$\$', '', content)
                chinese_count_2 += len(re.findall(r'[\u4e00-\u9fa5]', content))
                english_count_2 += len(re.findall(r'[a-zA-Z]', content))
                number_count_2 += len(re.findall(r'\d', content))
            
            if img_count_1 != img_count_2:
                return
            if latex_count_1 > latex_count_2: # 可能拆公式
                return
            if chinese_count_1 != chinese_count_2:
                return
            if english_count_1 != english_count_2:
                return
            if number_count_1 != number_count_2:
                return

            for item in data:
                new_html = item['new_html']
                target = item['answer']
                target.clear()
                target.extend(BeautifulSoup(new_html, 'html.parser').contents)
                print(f'成功修复答案分割：{new_html}')

            return

        def _execute_split_answer1(block):
            blanks1 = _get_blanks1(block)
            _execute_split_answer_with_blanks1(block, blanks1)
            blanks = _get_blanks(block)
            brackets = _get_brackets(block)
            _execute_split_answer_with_blanks1(block, blanks)
            if not brackets: return
            _execute_split_answer_with_blanks1(block, brackets)
            _execute_split_answer_with_blanks1(block, blanks + brackets)
        
        def _split_answer1():
            soup = BeautifulSoup(self.html_data, 'html.parser')
            blocks = _get_level1_blocks(soup)
            blocks = [block for block in blocks if re.search(r'data-label="answer"', ''.join([str(tag) for tag in block]))]
            for block in blocks: _execute_split_answer1(block)
            self.html_data = str(soup)
            return
        
        # - 作答大小题
        # - 一级试题
        _split_answer1()
        return self.html_data


    '''删掉解析末尾冗余的答案'''
    def remove_duplicate_answer(self):
        # > 按照题号分组
        # > 把每组抽取成题干、答案、解析的对象
        # > 判断是否需要删除解析末尾冗余的答案

        def _get_blocks(soup):
            elements = list(soup.find_all(True, recursive=False))
            questions = [q for q in elements if self.is_question1(q)]
            
            blocks = []
            for question in questions:
                block = []
                current = question
                
                # 收集后续节点直到终止条件
                while True:
                    block.append(current)
                    next_sib = current.next_sibling
                    
                    # 终止条件检测
                    if not next_sib or \
                        self.is_question1(next_sib) or \
                        self.is_material(next_sib) or \
                        self.is_header(next_sib):
                        break
                    
                    current = next_sib
                
                blocks.append(block)
            
            return blocks
        
        def _transfer_question(soup, blocks):
            questions = []
            for block in blocks:
                question = {}
                question['stem'] = []
                question['answer'] = []
                question['analysis'] = []
                for p in block:
                    if self.is_answer(p): question['answer'].append(p)
                    elif self.is_analysis(p): question['analysis'].append(p)
                    else: question['stem'].append(p)
                questions.append(question)
            return questions

        def _format_question(soup, questions):
            for question in questions:
                if not question['answer'] or not question['analysis']:
                    continue
                answer = ''.join([str(a) for a in question['answer']])
                if re.search(r'<table|<img', answer):
                    continue
                answer = re.sub(r'<[^<>]+>', '', answer)
                answer = answer.strip()
                answer = re.sub(r'^【.*?】', '', answer)
                # - 目前只支持选择题答案
                if not re.search(r'^[A-Z]+$', answer):
                    continue
                analysis = str(question['analysis'][-1])
                analysis = re.sub(r'<[^<>]+>', '', analysis)
                analysis = analysis.strip()
                if re.search(r'<table|<img', analysis):
                    continue
                if answer != analysis:
                    continue
                question['analysis'][-1].decompose()
            return

        soup = BeautifulSoup(self.html_data, 'html.parser')
        blocks = _get_blocks(soup)
        questions = _transfer_question(soup, blocks)
        _format_question(soup, questions)
        self.html_data = str(soup)
        return self.html_data


    def main(self):
        self.clear_n()
        self.split_answer()
        self.remove_duplicate_answer()
        return self.html_data
    

if __name__ == '__main__':

    input_path = os.path.join(os.path.dirname(__file__), 'test_post_html.html')
    output_path = os.path.join(os.path.dirname(__file__), 'test_post_html_output.html')

    if not os.path.exists(input_path):
        with open(input_path, 'w', encoding='utf-8') as f:
            f.write('<sample>Test HTML content</sample>')

    with open(input_path, 'r', encoding='utf-8') as f:
        html_content = f.read()

    post_processor = PostHtml(html_content)
    post_processor.split_answer()
    post_processor.remove_duplicate_answer()

    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(post_processor.html_data)
    
    print('test post html done.')
