# -*-coding:utf-8 -*-
# Author     ：wang<PERSON><PERSON>
# Email      ：<EMAIL>
# Time       ：2025/5/12 15:43
import asyncio
import re
import ast
from bs4 import BeautifulSoup, Tag

from app.basic.util import html_util, utils, time_util
from app.enums.prompt import GetPrompt
from app.enums.subject import Subject
from app.enums.node import ReplText, REPL_TEXT_LIST, QuestionType
from app.basic.api import deepseek_official
from app.basic.api.doubao import <PERSON>ubao, DBModel
from app.basic.log import logger
from app.basic.baseerror import VerifyError, TokenTooMuchError
from app.basic.util import db_util


class FixErrorBlockV2:

    def __init__(
            self,
            html_list: list,
            error_block_list: list,
            task_id: str,
            subject: str,
            stat: dict,
            round_index: int,
            success_sn_list: list,
            parent_id: str,
            tag: str,
    ):
        self.html_list = html_list
        self.error_block_list = error_block_list
        self.task_id = task_id
        self.subject = subject
        self.stat = stat
        self.round_index = round_index
        self.success_sn_list = success_sn_list
        self.parent_id = parent_id
        self.tag = tag
        # 失败日志
        self.error_log_list = []

    async def main(self):
        """
        处理 check 失败的 block
        """
        # 并发执行，控制并发数
        error_block_chunk_list = utils.chunk_v2(self.error_block_list, 50)
        for item_chunk_list in error_block_chunk_list:
            tasks = [self.fix_block(item) for item in item_chunk_list]
            await asyncio.gather(*tasks)
        self.html_list = [h for h in self.html_list if h]
        html_data = html_util.join_html(self.html_list, is_del_line=False)

        return html_data, self.stat

    async def fix_block(self, block_item):
        block = block_item['html_data']
        # 如果当前题在之前的轮次跑过了，则直接 return
        prefix_text = block_item['prefix_text']
        if prefix_text in self.success_sn_list:
            return

        min_line, max_line = block_item['min_line'], block_item['max_line']
        question_type = block_item['question_type']

        pred_block, json_data, model_res, sub_html_data = '', [], '', ''
        try:
            block = self.pre_block(block, question_type)
            pred_block, block_tag_cache = self.pre_repl_block_tag(block)
            model_res = await self.use_model_structure(pred_block, question_type)
            self.check_model_result(model_res)
            if question_type == QuestionType.material.name:
                new_json_data = self.post_material_block(model_res, block_tag_cache, pred_block)
            else:
                new_json_data = self.post_block(model_res, block_tag_cache, pred_block)
            sub_html_data = self.json_to_html(new_json_data, question_type)
            sub_html_data = self.post_html(sub_html_data)
            # check
            self.check_final_html(sub_html_data, block)

            # 把所有的数据写到开始的 line，其余 line 置空
            for i in range(min_line, max_line + 1):
                if i == min_line:
                    self.html_list[i] = sub_html_data
                else:
                    self.html_list[i] = ''
            self.stat[f'fixed_block_count_{self.round_index}'] += 1
            self.success_sn_list.append(prefix_text)
        except Exception as e:
            if 'TokenTooMuchError' in str(e):
                self.stat['fixed_block_count'] += 1
            error_info = (f"question_type={question_type}\n"
                          f"pred_block={pred_block}\n"
                          f"model_res={model_res}\n"
                          f"error_info={utils.format_error()}")
            logger.info(utils.format_error())
            db_util.html_fix_log_create(self.parent_id, self.task_id, self.subject, self.round_index, error_info, self.tag)

    def pre_block(self, block, question_type):
        """
        预处理 block
        此函数返回的 block 要在后面与算法返回结果在 char check 所以在增加和修改的时候要 review 一下会不会对 char check 有影响
        """
        # 选择题预清洗，把每一行开始，在公式里面的选项字母独立出来
        if question_type == QuestionType.choice.name:
            block_list = html_util.split_html_v2(block)
            for index, s in enumerate(block_list):
                s = re.sub(r'(<p[^>]*?>)\$\$\\mathrm{([A-D])}\.', lambda x: f'{x.group(1)}{x.group(2)}.$$', s)
                block_list[index] = s
            block = '\n'.join(block_list)

        return block

    def pre_repl_block_tag(self, block):
        """
        替换 block 内的 html 标签，减少对大模型输入的噪音
        """
        block_tag_cache = {}

        # table 表格放在最前面，因为表格内可能含有 blank bracket 等
        soup = BeautifulSoup(block, 'html.parser')
        tables = soup.find_all('table')
        for i, table in enumerate(tables, start=1):
            table_str = str(table)
            repl_text = f"#table{i}#"
            block_tag_cache[repl_text] = table_str
            table.replace_with(repl_text)
        block = str(soup)

        # 补齐大题题干
        block = re.sub(
            r'(<span[^<>]*?data-label="quest_num"[^<>]*?data-level="1"[^<>]*?>.*?</span>)(<span[^<>]*?data-label="quest_num"[^<>]*?data-level="2"[^<>]*?>.*?</span>)',
            lambda x: x.group(1) + f"{ReplText.BIG_QUESTION_BODY.value}\n" + x.group(2),
            block)

        i = 1
        def repl(m):
            nonlocal i
            text = m.group()
            repl_text = f'#bracket{i}#'
            block_tag_cache[repl_text] = text
            i += 1
            return repl_text
        # bracket
        block = re.sub(r'<span[^<>]*?data-label="bracket"[^<>]*?>(?<!</span>)[\S\s]*?</span>', repl, block)

        i = 1
        def repl(m):
            nonlocal i
            text = m.group()
            repl_text = f'#blank{i}#'
            block_tag_cache[repl_text] = text
            i += 1
            return repl_text
        # blank
        block = re.sub(r'<span[^<>]*?data-label="blank"[^<>]*?>(?<!</span>)[\S\s]*?</span>', repl, block)

        i = 1
        def repl(m):
            nonlocal i
            text = m.group()
            repl_text = f'#tag{i}#'
            block_tag_cache[repl_text] = text
            i += 1
            return repl_text
        # img
        block = re.sub(r'<img[^<>]*?>', repl, block)

        i = 1
        def repl(m):
            nonlocal i
            text = m.group()
            repl_text = f'#latex{i}#'
            block_tag_cache[repl_text] = text
            i += 1
            return repl_text
        # 公式
        block = re.sub(r'\$\$.*?\$\$', repl, block)

        # 清理 p 中原本的语义化标签
        def repl(m):
            text = m.group()
            text = re.sub(r' block-tag="explanation\d+"', '', text)
            text = re.sub(r' data-label="explanation"', '', text)
            text = re.sub(r' block-tag="answer\d+"', '', text)
            text = re.sub(r' data-label="answer"', '', text)
            text = re.sub(r' data-label="extra"', '', text)
            return text
        block = re.sub(r'<p[^<>]*?>', repl, block)

        # p 替换为 tag
        i = 1
        def repl(m):
            nonlocal i
            text = m.group()
            repl_text = f'#p{i}#'
            block_tag_cache[repl_text] = text
            i += 1
            return repl_text
        block = re.sub(r'<p[^<>]*?>', repl, block)

        # 清掉其余的无用标签
        block = re.sub(r'<[^<>]*?>', '', block)

        # 超过 token 限制，则直接报错
        if self.subject == Subject.english.name:
            length = len(re.split(r'\s+', block))
        else:
            length = len(block)
        if length > 6000:
            raise TokenTooMuchError('token 超过边界，不执行调用大模型')
        block = block.strip()
        return block, block_tag_cache

    async def use_model_structure(self, block, question_type):
        """
        使用大模型结构化
        第一轮：使用 豆包 32K 便宜
        第二轮还失败：使用 deepseek V3
        """
        prompt = GetPrompt.text_to_json(block, self.subject, question_type, version='v2')
        if self.round_index == 1:
            model_res, cost_token = await Doubao.async_chat(prompt, DBModel.V15_PRO_32.value, 0.1)
        else:
            if time_util.is_time_to_run_dp_official():
                # deepseek 官网
                model_res, cost_token = await deepseek_official.async_v3(prompt, temperature=0.1)
            else:
                model_res, cost_token = await Doubao.async_chat(prompt, DBModel.DS_V3.value, 0.1)

        self.stat[f'cost_token_{self.round_index}'] += cost_token
        model_res = model_res.replace('```JSON', '').replace('```', '').replace('```json', '').replace('json', '')
        model_res = model_res.strip()
        return model_res

    def post_block(self, model_res, block_tag_cache, pred_block):
        """
        后处理模型返回数据
        参数：
            block_tag_cache：block 内标签的缓存结果
            pred_block: 预处理后输入到大模型的 block
        """
        # 替换掉添加的文本
        for t in REPL_TEXT_LIST:
            model_res = model_res.replace(t, '')

        # 把大模型 YY 出来的标签干掉，只保留预期标签
        model_res = html_util.filter_tags(model_res, ['sn1', 'sn2', 'sn3', 'letter', 'source'])

        # 后处理大模型产出的结果
        json_data = self._convert_to_json(model_res, pred_block)
        json_data = self._post_model_res_json(json_data)

        def repl(value):
            # 添加 prefix
            value = self._add_prefix_tag(value)
            # 把 tag 替换回去
            for tag, text in block_tag_cache.items():
                value = value.replace(tag, text)
            # 补充 </p>
            value = value.strip()
            if value:
                value = value.replace('\n', '</p>\n')
                value = value + '</p>'
            # 清掉 table 两边 p
            if '</table>' in value:
                value = re.sub(r'<p[^<>]*?>\s*(<table[^<>]*?>)', lambda x: x.group(1), value)
                value = re.sub(r'</table></p>', '</table>', value)
            return value
        json_data = self._recursion_json(json_data, repl)
        return json_data

    def post_material_block(self, model_res, block_tag_cache, pred_block):
        """
        后处理材料题的模型返回数据
        参数：
            block_tag_cache：block 内标签的缓存结果
            pred_block: 预处理后输入到大模型的 block
        """
        # 替换掉添加的文本
        for t in REPL_TEXT_LIST:
            model_res = model_res.replace(t, '')

        # 把大模型 YY 出来的标签干掉，只保留预期标签
        model_res = html_util.filter_tags(
            model_res, ['sn1', 'sn2', 'sn3', 'letter', 'source', 'body', 'choice', 'answer', 'analysis'])

        json_data = self._convert_to_json(model_res, pred_block)

        def repl(value):
            # 添加 prefix
            value = self._add_prefix_tag(value)
            # 把 tag 替换回去
            for tag, text in block_tag_cache.items():
                value = value.replace(tag, text)
            # 补充 </p>
            value = value.strip()
            if value:
                value = value.replace('\n', '</p>\n')
                value = value + '</p>'

            # 清掉 table 两边 p
            if '</table>' in value:
                value = re.sub(r'<p[^<>]*?>\s*(<table[^<>]*?>)', lambda x: x.group(1), value)
                value = re.sub(r'</table></p>', '</table>', value)
            return value
        json_data = self._recursion_json(json_data, repl)
        return json_data

    def json_to_html(self, json_data, question_type):
        res = ''
        if question_type == QuestionType.material.name:
            res = self.material_json_to_html(json_data)
        else:
            for d in json_data:
                # d = self.pre_json_for_convert_html(d)
                sub_html = self.sub_json_to_html(d, question_type)
                res += sub_html
        return res

    def post_html(self, html_data):
        """
        后处理 sub html
        """
        # 连续 >= 2 个题号，则去掉题号标签
        html_list = html_util.split_html_v2(html_data)
        for d in html_list:
            pattern = re.compile(
                r'(<span[^>]*?data-label="quest_num" data-level="\d"[^>]*?>(（\d）\s*)</span>[\u200b\s]*){2,}')
            if pattern.search(d):
                tmp = pattern.search(d).group()
                tmp1 = re.sub(
                    r'<span[^>]*?data-label="quest_num" data-level="\d"[^>]*?>(（\d）\s*)</span>',
                    lambda x: x.group(1), tmp)
                html_data = html_data.replace(tmp, tmp1)

        return html_data

    def pre_json_for_convert_html(self, data):
        analysis = data.get('analysis', [])
        new_analysis = []
        box = []
        prefix_set = set()
        for index, item in enumerate(analysis):
            pred_item = re.sub(r'<p [^<>]*?>', '', item)
            # 预处理 prefix
            pattern = re.compile(r'<prefix>((?<!</prefix>)[\S\s]*?)</prefix>')
            if pattern.search(item):
                prefix_str = pattern.search(item).group(1)
            else:
                prefix_str = ''
            for s in prefix_set:
                if prefix_str and prefix_str in s:
                    item = pattern.sub('', item)
                    break

            if index == 0 and pred_item.startswith('<prefix>'):
                box.append(item)
            if not pred_item.startswith('<prefix>'):
                box.append(item)
            if index > 0 and pred_item.startswith('<prefix>'):
                new_analysis.append('\n'.join(box))
                box = [item]
            if len(analysis) == index + 1:
                new_analysis.append('\n'.join(box))
            if prefix_str:
                prefix_set.add(prefix_str)
        data['analysis'] = new_analysis
        return data

    def material_json_to_html(self, data):
        """
        材料题 json 转为 html
        参数：
            data: json_data
        """
        html_list = []
        for d in data:
            if not d.startswith(('<body>', '<choice>', '<answer>', '<analysis>')):
                raise VerifyError("大模型输出结构错误，不是以 <body>/<choice>/<answer>/<analysis> 开头")
            if d.startswith('<body>'):
                d = d.replace('<body>', '')
                d = self._handle_quest_num_tag(d)
                d = self._handle_source_tag(d)
            if d.startswith('<choice>'):
                d = d.replace('<choice>', '')
                d = self._handle_choice_tag(d)
            if d.startswith('<answer>'):
                d = d.replace('<answer>', '')
                d = self._handle_prefix_tag(d, is_discard=True)
                # answer 中不需要题号之间的 ■ 分隔符
                d = re.sub(r'</sn\d>.*?<sn\d>', lambda x: x.group().replace('■', ''), d)
                d = d.replace('<p ', '<p data-label="answer" ')
                d= self._handle_quest_num_tag(d)
            if d.startswith('<analysis>'):
                d = d.replace('<analysis>', '')
                # 去掉点睛的 prefix，材料题点睛不需要 discard
                d = d.replace('<prefix>【点睛】</prefix>', '【点睛】')
                d = self._handle_prefix_tag(d, is_discard=True)
                lst = html_util.split_html_v2(d)
                is_in_dj = False  # 是否进入【点睛】的范围
                for index, s in enumerate(lst):
                    if '【点睛】' in s:
                        is_in_dj = True
                    if is_in_dj:
                        s = s.replace('<p', '<p data-label="extra"')
                    else:
                        s = s.replace('<p', '<p data-label="explanation"')
                    lst[index] = s
                d = html_util.join_html(lst, is_del_line=False)
                d = self._handle_quest_num_tag(d)
            html_list.append(d)

        html_data = html_util.join_html(html_list, is_del_line=False)
        html_data = html_data.replace('<p', '<p data-check="ai_check"')

        # 后处理，去掉语义化标签，防止一些误标的遗漏掉
        html_data = re.sub(r'<sn\d>', '', html_data)
        html_data = re.sub(r'</sn\d>', '', html_data)
        html_data = html_data.replace('<source>', '').replace('</source>', '')
        html_data = html_data.replace('<prefix>', '').replace('</prefix>', '')
        html_data = html_data.replace('<letter>', '').replace('</letter>', '')
        return html_data

    def sub_json_to_html(self, data, question_type):
        """
        每个子 json 转为 html
        参数：
            data: json_data
            question_type: 试题类型
        """
        html_list = []
        origin_body, serial_number, serial_number_has_semic = '', '', ''
        # 题干
        if data.get('body'):
            body = origin_body = data['body']
            pattern = re.compile(r'<sn1>(.*?)</sn1>')
            if pattern.search(body):
                serial_number = pattern.search(body).group(1)
            serial_number_has_semic = f'<span data-label="quest_num" data-level="1">{serial_number}</span>'
            body = self._handle_quest_num_tag(body)
            body = self._handle_source_tag(body)
            # 大模型有时候会把选项误识别到题干中，这里做一下兼容
            if '<letter>' in body:
                body = self._handle_choice_tag(body)
            html_list.append(body)

        # 选项
        if data.get('choice'):
            choice = data['choice']
            choice = self._handle_choice_tag(choice)
            html_list.append(choice)

        # 答案
        if data.get('answer', ''):
            answer = data['answer']
            self._check_duplicate_sn(answer)
            answer = self._handle_prefix_tag(answer, is_discard=True)
            answer = self._handle_quest_num_tag(answer)
            answer = answer.replace('<p', '<p data-label="answer"')
            html_list.append(answer)

        # 解析
        if data.get('analysis'):
            analysis = data['analysis']
            analysis_list = analysis.split('\n')
            # 把解析分为三组，整体解析，小题解析，额外内容
            # 1.按照 prefix 把解析拆分三组
            # 2.如果三组都不是空，则分别映射到整体解析，小题解析，额外内容
            # 3.如果其中两组不是空，则判断有无额外内容；如果有，则两组分别为小题解析，额外内容；如果没有，则两组分别为整体解析，小题解析
            # 4.如果其中一组不是空，则判断是不是额外内容；如果是，则为额外内容；如果不是则为小题解析
            prefix_index_list = []
            # 解析组，最多有两个 prefix

            prefix_max_count = 2
            for index, s in enumerate(analysis_list):
                if '<prefix>' not in s:
                    continue
                if prefix_max_count == 0:
                    analysis_list[index] = self._handle_prefix_tag(s, is_discard=False)
                else:
                    prefix_max_count -= 1
                    prefix_index_list.append(index)
            # 分组索引必须从 0 开始
            if prefix_index_list and prefix_index_list[0] != 0:
                prefix_index_list.insert(0, 0)
            # 获取切片的分组
            slice_list = []
            for index, prefix_index in enumerate(prefix_index_list):
                if index > len(prefix_index_list) - 2:
                    slice_list.append((prefix_index,))
                    break
                slice_list.append((prefix_index, prefix_index_list[index + 1]))
            # 根据切片分组的数量去写入对应的数组
            analysis_summary, analysis_detail, extra = '', '', ''
            if len(slice_list) == 3:
                lst1 = analysis_list[slice_list[0][0]: slice_list[0][1]]
                lst2 = analysis_list[slice_list[1][0]: slice_list[1][1]]
                lst3 = analysis_list[slice_list[2][0]:]
                analysis_summary, analysis_detail, extra = '\n'.join(lst1), '\n'.join(lst2), '\n'.join(lst3)
            elif len(slice_list) == 2:
                lst1 = analysis_list[slice_list[0][0]: slice_list[0][1]]
                lst2 = analysis_list[slice_list[1][0]:]
                s1, s2 = '\n'.join(lst1), '\n'.join(lst2)
                if '【点睛】' in s2:
                    analysis_detail = s1
                    extra = s2
                else:
                    analysis_summary = s1
                    analysis_detail = s2
            elif len(slice_list) == 1:
                lst1 = analysis_list[slice_list[0][0]:]
                s1 = '\n'.join(lst1)
                if '【点睛】' in s1:
                    extra = s1
                else:
                    analysis_detail = s1
            else:
                raise VerifyError("解析分组检查不通过！")

            # 处理解析前缀和解析题号
            # 前缀部分：
            # 大小题：保留【分析】前缀，删除【详解】前缀
            # 选择、填空、作答单题：【分析】和【详解】都保留前缀
            # 题号语义化部分：
            # 大小题：大题解析中不需要题号语义化，大题解析前补充带标签的大题题号；小题解析中需要题号语义化
            # 小题：全部都不需要题号语义化
            analysis_summary = self._handle_quest_num_tag(analysis_summary, is_sem=False)
            if question_type == QuestionType.material.name or (question_type == QuestionType.other.name and '<sn2' in origin_body):
                is_discard = True
                analysis_detail = self._handle_quest_num_tag(analysis_detail)
                analysis_summary = re.sub(r'<p [^<>]*?>', lambda x: x.group() + serial_number_has_semic, analysis_summary, count=1)
            else:
                is_discard = False
                analysis_detail = self._handle_quest_num_tag(analysis_detail, is_sem=False)
            analysis_detail = self._handle_prefix_tag(analysis_detail, is_discard=is_discard)
            analysis_summary = self._handle_prefix_tag(analysis_summary, is_discard=False)
            extra = self._handle_prefix_tag(extra, is_discard=False)
            # 添加解析标签
            # 大题对 extra 添加 extra 标签，小题对 extra 添加 explanation 标签
            analysis_summary = analysis_summary.replace('<p', '<p data-label="explanation"')
            analysis_detail = analysis_detail.replace('<p', '<p data-label="explanation"')
            if question_type in (QuestionType.other.name, QuestionType.material.name):
                extra = extra.replace('<p', '<p data-label="extra"')
            else:
                extra = extra.replace('<p', '<p data-label="explanation"')

            if analysis_summary:
                html_list.append(analysis_summary)
            if analysis_detail:
                html_list.append(analysis_detail)
            if extra:
                html_list.append(extra)

        html_data = html_util.join_html(html_list, is_del_line=False)
        html_data = html_data.replace('<p', '<p data-check="ai_check"')

        # 后处理，去掉语义化标签，防止一些误标的遗漏掉
        html_data = re.sub(r'<sn\d>', '', html_data)
        html_data = re.sub(r'</sn\d>', '', html_data)
        html_data = html_data.replace('<source>', '').replace('</source>', '')
        html_data = html_data.replace('<prefix>', '').replace('</prefix>', '')
        html_data = html_data.replace('<letter>', '').replace('</letter>', '')
        return html_data

    def check_model_result(self, model_res: str):
        """
        检查算法返回的结果
        参数：
            model_res: 算法返回的结果字符串
        """
        # 对于选择题，不能同时存在 <letter>①</letter> 和 <letter>A</letter>
        if re.search(r'<letter>①\s*</letter>', model_res) and re.search(r'<letter>A.</letter>', model_res):
            raise VerifyError("选择题选项字母语义化错误！")

    def check_final_html(self, html_data, block):
        """
        检查最终输出的 html
        参数：
            html_data: 输出的 html 数据
            block： 输出的 html block 数据
        """
        self._check_line_id(html_data)
        self._check_html_tag(html_data, block)
        self._check_char_num(html_data, block)

    def _check_line_id(self, html_data: str):
        """
        检查每一行的行号，如果当前行号比之前的行号小，则报错
        """
        html_list = html_util.split_html_v2(html_data)
        pre_line = -1
        for s in html_list:
            pattern = re.compile(r'<p[^>]*?line="(\d+)[^>]*?">')
            line = int(pattern.search(s).group(1)) if pattern.search(s) else -1
            if line == -1:
                continue
            else:
                if pre_line > line:
                    raise VerifyError("大模型输出的内容位置发生了变动，请检查！")
                pre_line = line

    def _check_html_tag(self, html_data: str, block: str):
        """
        检查 tag 的数量
        """
        if block.count('<img') != html_data.count('<img'):
            raise VerifyError("HTML <img 数量不一致！")
        if block.count('<table') != html_data.count('<table'):
            raise VerifyError("HTML <table 数量不一致！")

        # 输出的 p 和 span 开闭合标签数量检查
        if html_data.count('<p') != html_data.count('</p'):
            raise VerifyError("HTML p 标签不成对！")
        # if html_data.count('<span') != html_data.count('</span'):
        #     raise VerifyError("HTML span 标签不成对！")

        # p 内不能包含 table div p
        soup = BeautifulSoup(html_data, 'html.parser')
        for p in soup.find_all('p'):
            for child in p.children:
                if isinstance(child, Tag) and child.name in ['table', 'div', 'p']:
                    raise VerifyError("HTML p 标签内包含 table|div|p")

    def _check_char_num(self, html_data, block):
        """
        字符数类的 check
        """
        def raise_error():
            raise VerifyError("字符数检查失败！")

        block = html_util.del_html_tag(block)
        model_res = html_util.del_html_tag(html_data)

        # 对比中文和英文字符串数量
        block_cn_count, block_en_count = utils.count_chinese_english_chars(block)
        model_cn_count, model_en_count = utils.count_chinese_english_chars(model_res)
        if block_cn_count != model_cn_count:
            raise_error()
        if block_en_count != model_en_count:
            raise_error()

    def _check_duplicate_sn(self, data):
        lst = re.findall(r'<sn\d>.*?</sn\d>', data)
        if len(lst) != len(set(lst)):
            raise VerifyError("答案或者解析内有重复题号！")

    def _convert_to_json(self, model_res: str, block: str):
        """
        把模型返回的结果转换成 Json 数据
        """
        try:
            json_data = ast.literal_eval(model_res)
            return json_data
        except Exception as e:
            raise VerifyError(f"{self.task_id} block={block} 大模型返回结果结构化失败")

    def _recursion_json(self, data, repl):
        if isinstance(data, dict):
            return {key: self._recursion_json(value, repl) for key, value in data.items()}
        elif isinstance(data, list):
            return [self._recursion_json(item, repl) for item in data]
        else:
            return repl(data)

    def _handle_quest_num_tag(self, s, is_sem=True):
        if is_sem:
            s = re.sub(r'<sn(\d)>', lambda x: f'<span data-label="quest_num" data-level="{x.group(1)}">', s)
            s = re.sub(r'</sn(\d)>', '</span>', s)
        else:
            s = re.sub(r'<sn(\d)>', '', s)
            s = re.sub(r'</sn(\d)>', '', s)
        return s

    def _handle_source_tag(self, s):
        s = s.replace('<source>', '<span data-label="source">')
        s = s.replace('</source>', '</span>')
        return s

    def _handle_choice_tag(self, s):
        s = s.replace('<letter>', '<span data-label="choice_option">')
        s = s.replace('</letter>', '</span>')
        return s

    def _handle_prefix_tag(self, s, is_discard):
        if is_discard:
            s = re.sub(
                r'<prefix>(.*?)</prefix>',
                lambda x: html_util.add_discard_span(x.group(1)),
                s
            )
        else:
            s = s.replace('<prefix>', '').replace('</prefix>', '')
        return s

    def _complete_answer_delimiter(self, json_data):
        """
        补全答案分割符
        """
        def delimiter_by_sn(data, is_answer=False):
            """
            根据 sn 拆分
            """
            result = {}

            # 使用正则分割题目段落
            segments = re.split(r'(<sn[12]>.*?</sn\d>)', data)
            current_key = None

            for seg in segments:
                if not seg.strip():
                    continue
                # 匹配主标题
                if m := re.match(r'<sn1>(\d+\.)</sn1>', seg):
                    current_key = m.group(1)
                    result[current_key] = ""
                # 匹配子标题
                elif m := re.match(r'<sn2>（(\d+)）</sn2>', seg):
                    current_key = f"（{m.group(1)}）"
                    result[current_key] = ""
                elif current_key:
                    # 去除段落标记保留内容
                    clean_seg = re.sub(r'#p\d+#', '', seg).strip()
                    result[current_key] = clean_seg

            if is_answer:
                for sn, s in result.items():
                    s = re.sub(r'(</p>)|(<p[^>]*?>)', '', s).strip()
                    # 先按照 \n 分割
                    temp_list = s.split('\n')
                    # 再按照黑块分割
                    new_temp_list = []
                    for t in temp_list:
                        new_temp_list += t.split('■')
                    new_temp_list = [t for t in new_temp_list if t]
                    result[sn] = new_temp_list
            return result

        def split_answer(ans_list, origin_answer, blank_count):
            for a in ans_list:
                # 如果是一个 tag 则认为不可拆
                if re.search(r'^<[^>]*?>$', a):
                    continue
                # 先按照空格拆
                pred_a = re.sub(r'\s+', '■', a)

                def repl(m):
                    latex_str = m.group()
                    latex_str = re.sub(r'(\\;)+', '\\;', latex_str)
                    latex_str = latex_str.strip().strip('$')

                    parts = re.split(r'\\;+(?![^{}]*\})', latex_str)
                    filtered = [f'$${p.strip()}$$' for p in parts]
                    result = '■'.join(filtered)
                    result = result.replace('$$$$', '')
                    return result
                # 按照公式中的空格拆
                pred_a = re.sub(r'\$\$.*?\$\$', repl, pred_a)
                pred_a = pred_a.lstrip('■')
                origin_answer = origin_answer.replace(a, pred_a)
            return origin_answer

        # 遍历每个题目数据
        for index, item in enumerate(json_data):
            answer = item.get('answer', '')
            body = item.get('body', '')

            body_sn_dict = delimiter_by_sn(body)
            for k, v in body_sn_dict.items():
                blank_count = v.count('data-label="blank"')
                body_sn_dict[k] = blank_count
            answer_sn_dict = delimiter_by_sn(answer, is_answer=True)

            for sn, ans_list in answer_sn_dict.items():
                blank_count = body_sn_dict.get(sn, -1)
                if blank_count != 0 and blank_count != len(ans_list):
                    answer = split_answer(ans_list, answer, blank_count)
            json_data[index]["answer"] = answer

        return json_data

    def _post_model_res_json(self, data):
        """
        后处理大模型返回的 json 化的内容
        """
        def _helper(d):
            # 把每一行没有以 p 开头的，合到上一行去
            lst = d.split('\n')
            for i, s in enumerate(lst):
                if '#table' in s:
                    continue
                if i > 0 and not re.search(r'^#p\d+#', s):
                    lst[i - 1] = lst[i - 1] + s
                    lst[i] = ''
            lst = [s for s in lst if s]
            d = '\n'.join(lst)
            return d

        for item in data:
            if item.get('body'):
                body = item['body']
                body = _helper(body)
                item['body'] = body

            # analysis_list = item.get('analysis', [])
            # for index, a in enumerate(analysis_list):
            #     if re.search(r'#p\d+#', a):
            #         continue
            #     if index > 0:
            #         analysis_list[index - 1] = analysis_list[index - 1] + a
            #         analysis_list[index] = ''
            # analysis_list = [a for a in analysis_list if a]
            # item['analysis'] = analysis_list
        return data

    def _add_prefix_tag(self, data: str):
        """
        根据规则添加标签
        """
        lst = data.split('\n')
        for index, s in enumerate(lst):
            prefix_keywords = [
                '【详解】解：', '【解析】解：', '【详解】', '【分析】', '【导语】', '【解析】', '【答案】', '【点睛】', '解：']
            for k in prefix_keywords:
                if k not in s:
                    continue
                s = s.replace(k, f'<prefix>{k}</prefix>')
                lst[index] = s
                break
        data = '\n'.join(lst)
        return data
