# -*-coding:utf-8 -*-
# Author     ：wang<PERSON><PERSON>
# Email      ：<EMAIL>
# Time       ：2025/4/19 11:31
import re

from app.enums.app_key import AppKey
from app.basic.util import html_util


class PostHtml:

    def __init__(self, task_id: str, html_data: str, app_key: str, is_wps: bool):
        self.task_id = task_id
        self.html_data = html_data
        self.app_key = app_key
        self.is_wps = is_wps

    def main(self):
        self.html_data = PostCommon(self.html_data, self.is_wps).main()
        if self.app_key == AppKey.JXW_API.value:
            self.html_data = PostJXWHtml(self.task_id, self.html_data).main()

        # 去掉 html 的 \n
        self.html_data = self.html_data.replace('\n', '')
        return self.html_data


class PostCommon:

    def __init__(self, html_data: str, is_wps: bool):
        self.html_data = html_data
        self.is_wps = is_wps

    def main(self):

        # ai-edit 的 wps 流程后处理
        # Tips:
        # 1.有材料，可能出现多个一级题号  2.没有材料，不会有多个一级题号
        if self.is_wps and not self.is_material_question():
            html_list = html_util.split_html_v2(self.html_data)
            # 除第一个一级题号，其他的一级题号换为二级
            has_level_1 = False
            for index, html_str in enumerate(html_list):
                if 'data-label="quest_num"' in html_str and 'data-level="1"' in html_str:
                    if not has_level_1:
                        has_level_1 = True
                        continue
                    else:
                        html_str = html_str.replace(' data-label="quest_num"', '')
                        html_str = html_str.replace(' data-level="1"', '')
                        html_list[index] = html_str
            self.html_data = html_util.join_html(html_list, is_del_line=False)

        # 最后，去掉 </p> 后的换行，注意，是最后！
        self.html_data = self.html_data.replace('</p>\n', '</p>')

        return self.html_data

    def is_material_question(self):
        return 'data-label="material_start_separator"' in self.html_data


class PostJXWHtml:

    def __init__(self, task_id: str, html_data: str):
        self.task_id = task_id
        self.html_data = html_data

    def main(self):
        # 不要三级题号
        self.html_data = re.sub(
            r'<span data-label="quest_num" data-level="3">((?<!</span>)[\S\s]*?)</span>',
            lambda x: x.group(1), self.html_data)
        return self.html_data