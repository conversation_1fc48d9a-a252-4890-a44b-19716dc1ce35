# -*-coding:utf-8 -*-
# Author     ：wang<PERSON><PERSON>
# Email      ：<EMAIL>
# Time       ：2025/2/27 19:25
import re

from app.basic.log import logger


class GetTitle:
    """
    获取 html 中的标题，
    """
    def __init__(self, html_list, subject, task_id):
        self.html_list = html_list
        self.subject = subject
        self.task_id = task_id

    def main(self):
        logger.info(f"{self.task_id} GetTitle start")
        title_list = []
        for index, html_str in enumerate(self.html_list):
            if len(self.html_list) - 1 > index > 0:
                pre_html_str, next_html_str = self.html_list[index - 1], self.html_list[index + 1]
            else:
                pre_html_str, next_html_str = '', ''

            if '从颜色来看' in html_str:
                print()

            is_title, is_title_verified = self.is_title(html_str, pre_html_str, next_html_str)
            # 把 html 中原来的 header 标签去掉
            if 'data-label="header"' in html_str:
                html_str = html_str.replace(' data-label="header"', '')
                html_str = re.sub(r' data-level="\d+"', '', html_str)
                self.html_list[index] = html_str

            if not is_title:
                continue

            title_str = self.get_title_str(html_str)
            title_list.append({
                'line_num': index,
                'title_str': title_str,
                # 默认都是 False，语义化结果不可信
                'is_title_verified': is_title_verified
            })

        title_list = self.clean_title(title_list)
        logger.info(f"{self.task_id} GetTitle success")
        return title_list

    def is_title(self, html_str, pre_html_str, next_html_str):
        """
        根据各种规则，判断是否是标题
        下面的规则判断有先后顺序
        1. 一定不是标题的先排除掉
        2. 可能是标题的判断
        3. 一定是标题的判断
        """
        is_seem_header = True if 'data-label="header"' in html_str else False

        cleaned_html_str = self.clean_html(html_str)
        title_len = len(cleaned_html_str)
        # 有很长的标题，例如：广东省东莞中学松山湖学校，深圳大学附属中学2023-2024学年高二下学期5月期中地理试题
        if title_len > 50:
            return False, False

        # 如果有特殊标签，则不是标题
        spec_label_list = [
            'data-label="blank"', 'data-label="choice_option"', 'data-label="bracket"',
            'data-label="answer"'
        ]
        for l in spec_label_list:
            if l in html_str:
                return False, False

        # 如果当前行的上一行和下一行都是楷体，则本行不是标题
        # if 'font-family:楷体' in pre_html_str and 'font-family:楷体' in next_html_str:
        #     return False
        # 如果是楷体，则不是标题
        if 'font-family:楷体' in html_str:
            return False, False
        # 本行居中，下一行是楷体，则本行不是标题（文言文或者诗词的题目）
        if 'align-center' in html_str and 'font-family:楷体' in next_html_str:
            return False, False
        # 如果居右，则不是标题
        if 'align-right' in html_str:
            return False, False

        # 根据长度判断可能为标题的
        if not cleaned_html_str \
            or re.search(r'^[!"#$%&\'()*+,-./:;<=>?@[\\\]^_`{|}~，。、；：？！…—（）【】《》‘’“”\s]*$', cleaned_html_str):
            return False, False
        if re.search(r'.年级', cleaned_html_str) and re.search(r'.次', cleaned_html_str):
            return True, True
        # 根据关键词判断，肯定是标题的
        if re.search(r'(试题)|(测试)|(练习卷)|(试卷)', cleaned_html_str):
            return True, True
        # 需要前端流程标题标记的辅助加上关键词判断
        if is_seem_header and re.search(r'(省)|(市)|(\d+年)', cleaned_html_str):
            return True, True
        if title_len <= 18:
            return True, False

        # 前序流程标的标题，可能为标题
        if is_seem_header:
            return True, False

        return False, False

    def get_title_str(self, html):
        def repl(m):
            t = m.group()
            if '<img' in t:
                image_text_search = re.search(r'data-image-text="(.*?)"', t)
                if image_text_search:
                    return image_text_search.group(1)
                else:
                    return ''
            else:
                return ''

        text = re.sub(r'<[^<>]*?>', repl, html).strip()
        text = self.clean_html(text)
        return text

    def clean_html(self, html):
        html = re.sub(r'<[^<>]*?>', '', html)
        html = re.sub(r'😊para\d+😊', '', html)
        html = re.sub(r'😊img\d+😊', '', html)
        html = re.sub(r'😊tbl\d+😊', '', html)
        html = re.sub(r'\s+', ' ', html)
        html = re.sub(r'\$\$.*?\$\$', '', html)
        return html

    def clean_title(self, title_list):
        for item in title_list:
            title_str = item['title_str']
            title_str = re.sub(r'\\;', ' ', title_str)
            title_str = re.sub(r'\s+', ' ', title_str)
            item['title_str'] = title_str

        return title_list
