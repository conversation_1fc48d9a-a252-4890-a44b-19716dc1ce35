# -*-coding:utf-8 -*-
# Author     ：wang<PERSON><PERSON>
# Email      ：<EMAIL>
# Time       ：2025/3/8 10:49
import json
import re
import time

import requests

from app.enums.app_key import AppKey
from app.enums.subject import SubjectUtil, Subject
from app.xdoc_html_fix.clean.clean_latex import CleanLatex
from app.xdoc_html_fix.fix.latex_error_fix import LatexErrorFix
from app.xdoc_html_fix.clean.clean_by_entirety import CleanByEntirety
from app.xdoc_html_fix.clean.clean_by_line import CleanByLine
from app.xdoc_html_fix.fix.latex_zx_fix import LatexZXFix
from app.xdoc_html_fix.multi_round_fix.main import MultiRoundFix
from app.xdoc_html_fix.clean.semic_by_rules import SemicByRules
from app.xdoc_html_fix.post.post_latex import PostLatex
from app.xdoc_html_fix.post.post_html import PostHtml
from app.basic.api.mq import MQ
from app.basic.util import utils, html_util
from app.basic.log import logger
from app.basic.storagehandler import storage


class XdocHtmlFixWorker:

    def __init__(
            self, task_id: str, html_url: str, subject: str, app_key: str, bucket_name: str,
            upload_path: str, word_type: str, is_ai_edit: bool, task_type: str, is_test: bool = False,
            tag: str = 'jxw'):
        self.task_id = task_id
        self.html_url = html_url
        self.subject = subject
        self.app_key = app_key
        self.bucket_name = bucket_name
        self.upload_path = upload_path
        self.word_type = word_type
        self.is_ai_edit = is_ai_edit
        self.task_type = task_type

        self.is_test = is_test
        self.tag = tag

        self.is_wps = True if re.search(r'^wps-', self.word_type) else False
        self.html_url = self.html_url.replace('-internal.aliyuncs.com', '.aliyuncs.com')
        self.html_data = requests.get(self.html_url).content.decode('utf-8')
        # 初始化 HTML 格式
        html_list = html_util.split_html_v2(self.html_data, is_add_line=False)
        self.html_data = '\n'.join(html_list)

        self.stat = {}
        self.callback_data = {
            'task_id': self.task_id,
            'task_type': self.task_type,
            'task_status': False,  # True 成功 False 失败
            'callback_info': {
                'task_id': self.task_id,
                'status': -1,  # 0 成功  1 失败
                'info': '',
                'result': '',
                # 统计
                'stat': self.stat,
            }
        }

    async def main(self):
        try:
            start_time = time.time()
            logger.info(f"{self.task_id} XdocHtmlFixWorker start.")
            if self.is_in_white_list():
                await self.task()
            else:
                logger.info(f"{self.task_id} XdocHtmlFixWorker 不在白名单内，直接返回 html data")

            _, object_name = storage.put_object_from_string(self.bucket_name, self.upload_path, self.html_data)

            # 测试
            if self.is_test:
                from app.basic.api.xdoc import XdocApi
                json_data = XdocApi.html_to_json(self.html_data)
                print(json.dumps(json_data, ensure_ascii=False))

            self.callback_data['task_status'] = True
            self.callback_data['callback_info']['status'] = 0
            self.callback_data['callback_info']['result'] = object_name
            end_time = time.time()
            self.stat_merge(int(end_time - start_time))
            logger.info(f"{self.task_id} XdocHtmlFixWorker success.")
        except Exception as e:
            error_info = f'{self.task_id} XdocHtmlFixWorker error, {utils.format_error()}'
            logger.info(error_info)
            self.callback_data['callback_info']['status'] = 1
            self.callback_data['callback_info']['info'] = error_info

        if not self.is_test:
            MQ.callback_task(self.callback_data)
        else:
            return self.callback_data['callback_info']

    async def task(self):
        # html 整体和按行清理
        self.html_data = CleanByEntirety(self.html_data, self.subject, self.task_id).main()
        self.html_data = CleanByLine(self.html_data, self.subject, self.task_id).main()

        # HTML 多轮修复
        self.html_data, stat = await MultiRoundFix(
            self.html_data, self.subject, self.task_id, self.html_url, self.tag, self.task_type, self.is_ai_edit,
            self.is_wps
        ).main()
        self.stat.update({'html_fix': stat})

        # latex 预处理
        if not self.is_ai_edit:
            self.html_data = CleanLatex(self.html_data, self.subject).main()

        # latex 异常修复
        if not self.is_ai_edit:
            self.html_data, stat = await LatexErrorFix(self.html_data, self.subject, self.task_id).main()
            self.stat.update({'latex_error_fix': stat})

        # latex 正斜体修复
        if not self.is_ai_edit:
            self.html_data, stat = await LatexZXFix(self.html_data, self.subject, self.task_id).main()
            self.stat.update({'latex_zx_fix': stat})

        # latex 异常修复（正斜体处理完可能出现几个错误公式，在这里重新修复一次）
        if not self.is_ai_edit:
            self.html_data, stat = await LatexErrorFix(self.html_data, self.subject, self.task_id).main()
            self.stat.update({'latex_error_fix_2': stat})

        # 后处理 HTML
        self.html_data = PostLatex(self.html_data, self.task_id).main()
        self.html_data = PostHtml(self.task_id, self.html_data, self.app_key, self.is_wps).main()

    def is_in_white_list(self):
        if self.is_test:
            return True

        # 只跑九学王和合心
        if self.app_key not in AppKey.get_values():
            return False

        # 跑所有的 ai-edit 的任务
        if self.is_ai_edit:
            if self.is_wps:
                return True
            else:
                if self.subject in (Subject.english.name, Subject.chinese.name):
                    return False
                else:
                    return True
        else:
            # 不跑英语学科和语文学科
            if self.subject in (Subject.english.name, Subject.chinese.name):
                return False
            else:
                return True

    def stat_merge(self, total_cost_time: float):
        total_token = 0
        logger.info(f'{self.task_id} >>> stat={self.stat}')
        for k, item in self.stat.items():
            if k == 'html_fix':
                total_token += (item.get('cost_token_1', 0) + item.get('cost_token_2', 0))
            else:
                total_token += item['cost_token']
        self.stat.update({'total': {'cost_token': total_token, 'cost_time': total_cost_time}})
